﻿using System.Net;
using AutoFixture;
using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services;
using DWH.ProjectServices.API.Services.Helper.Interface;
using FluentAssertions;
using Moq;
using static DWH.ProjectServices.API.Services.Helper.Records.PeriodicityOperationRecords;


namespace DWH.ProjectServices.API.UnitTests.Services;

public class QCPeriodServiceUTest
{
    private readonly IFixture _fixture;
    private readonly IMapper _mapper;
    private readonly Mock<IQCPeriodRepository> _qcPeriodRepositoryStub;
    private readonly QCPeriodService _qcPeriodService;
    private readonly Mock<IDateApiClient> _dateApiClient;
    private readonly Mock<IOperationHelper> _operationHelper;
    private readonly Mock<IBaseProjectRepository> _baseProjectRepositoryStub;
    private readonly Mock<IOutBoxItemRepository> _outBoxItemRepositoryStub;

    public QCPeriodServiceUTest()
    {
        _fixture = new Fixture();
        var mappingConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new Infrastructure.Persistence.Profile.QCProjectProfile());
        });
        _mapper = mappingConfig.CreateMapper();
        _qcPeriodRepositoryStub = new Mock<IQCPeriodRepository>();
        _dateApiClient = new Mock<IDateApiClient>();
        _operationHelper = new Mock<IOperationHelper>();
        _outBoxItemRepositoryStub = new Mock<IOutBoxItemRepository>();

        _baseProjectRepositoryStub = new Mock<IBaseProjectRepository>();
        _qcPeriodService = new QCPeriodService(_mapper, _qcPeriodRepositoryStub.Object, _outBoxItemRepositoryStub.Object,
          _operationHelper.Object, _baseProjectRepositoryStub.Object);
    }
    [Fact]
    public async Task CreateBulkQCPeriodAsync_When_QCPeriodsAreCreated_Expect_CorrectResponse()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "testUser";

        var mockOperationHelper = new Mock<IOperationHelper>();
        var mockQCPeriodRepository = new Mock<IQCPeriodRepository>();

        var service = new QCPeriodService(
            _mapper,
            mockQCPeriodRepository.Object,
            _outBoxItemRepositoryStub.Object,
            mockOperationHelper.Object,
            _baseProjectRepositoryStub.Object
        );

        var mockBaseQCPeriod = new QCPeriod
        {
            Id = qcProjectId * 1000,
            PeriodId = qcProjectId * 100,
            Periods = new List<Period>
            {
                new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
                new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
            }
        };

        mockOperationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(mockBaseQCPeriod);

        mockOperationHelper
            .Setup(helper => helper.GetPeriodShortNameAsync(It.IsAny<long>()))
            .ReturnsAsync("Jan23");

        mockOperationHelper
            .Setup(helper => helper.GetPeriodicityIdAsync(It.IsAny<int>()))
            .ReturnsAsync(4); // Monthly or any Other Periodicity for the mock

        mockOperationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302 });

        mockOperationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 1001),
                new ShiftedPeriod(1, 1002)
            });

        mockQCPeriodRepository
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ReturnsAsync((QCPeriod qcPeriod) => new QCPeriod
            {
                Id = 12345,
                PeriodId = qcPeriod.PeriodId
            });

        // Act
        var result = await service.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeEmpty();

        foreach (var record in result.BulkQCPeriods)
        {
            record.QCProjectId.Should().Be(qcProjectId);
            record.QCPeriodId.Should().BeGreaterThan(0);
            record.StatusCode.Should().Be((int)HttpStatusCode.Created);
            record.StatusMsg.Should().Contain("QC periods created successfully");
        }
    }


    [Fact]
    public async Task AddAsyncQCPeriod_When_SuccessfullyAdded_Expect_SuccessfulResult()
    {
        // Arrange
        var qcPeriod = _fixture.Create<QCPeriod>();

        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(qcPeriod));

        // Act
        Func<Task> act = async () => await _qcPeriodService.AddAsyncQCPeriod(qcPeriod);

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task AddAsyncQCPeriod_When_PeriodAlreadyExists_Expect_EntityAlreadyExistsException()
    {
        // Arrange
        var qcPeriod = _fixture.Create<QCPeriod>();

        _qcPeriodRepositoryStub
            .Setup(repo => repo.CheckIfQCPeriodExists(qcPeriod.QCProjectId, qcPeriod.PeriodId))
            .ReturnsAsync(true);

        // Act
        Func<Task> act = async () => await _qcPeriodService.AddAsyncQCPeriod(qcPeriod);

        // Assert
        await act.Should().ThrowAsync<EntityAlreadyExistsException>()
            .WithMessage("DUPLICATION_ENTITY");
    }


    [Fact]
    public async Task EditQCPeriodAsync_WhenCalled_WithInvalidIdInModel_Throws_Exception()
    {
        // Arrange
        var qcPeriodId = 1;
        var qcPeriodEditRequest = _fixture.Create<QCPeriodEdits>();
        var qcStatusesToReturn = new List<int> { };
        var existingPeriods = _fixture.Create<QCPeriodWithBPIdResponse>();
        existingPeriods.Id = qcPeriodId;

        _qcPeriodRepositoryStub
            .Setup(repo => repo.GetQCPeriodAsync(qcPeriodId))
            .ReturnsAsync(existingPeriods);
        _qcPeriodRepositoryStub
            .Setup(repo => repo.EditPeriodAsync(qcPeriodId, qcPeriodEditRequest))
            .ThrowsAsync(new EntityNotExistsException($"No Period exists with Id {qcPeriodId}"));
        _baseProjectRepositoryStub.Setup(repo => repo.GetBaseProjectWithQCStatus(It.IsAny<QCStatuses>()))
           .ReturnsAsync((qcStatusesToReturn));

        // Act
        Func<Task> act = async () => await _qcPeriodService.EditPeriodAsync(qcPeriodId, qcPeriodEditRequest);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>()
            .WithMessage($"ENTITY_NOT_EXISTS");
    }


    [Fact]
    public async Task EditQCPeriodAsync_WhenCalled_WithValidModel_UpdatesSuccessfully()
    {
        // Arrange
        var qcPeriodId = 1;
        var qcPeriodEditRequest = _fixture.Create<QCPeriodEdits>();
        var qcStatusesToReturn = new List<int> { };

        var existingPeriods = _fixture.Create<QCPeriodWithBPIdResponse>();
        existingPeriods.Id = qcPeriodId;

        var expectedResultRepository = _fixture.Create<QCPeriod>();
        expectedResultRepository.Id = qcPeriodId;

        var expectedResultService = new QCPeriodEditResponse
        {
            QCProjectId = 1,
            Periods = _fixture.CreateMany<Period>(1).ToList()
        };

        foreach (var period in expectedResultService.Periods)
        {
            period.QCPeriodId = qcPeriodId;
        }

        _qcPeriodRepositoryStub
            .Setup(repo => repo.GetQCPeriodAsync(qcPeriodId))
            .ReturnsAsync(existingPeriods);

        _qcPeriodRepositoryStub
            .Setup(repo => repo.EditPeriodAsync(It.IsAny<long>(), It.IsAny<QCPeriodEdits>()))
            .ReturnsAsync(expectedResultRepository);
        _baseProjectRepositoryStub.Setup(repo => repo.GetBaseProjectWithQCStatus(It.IsAny<QCStatuses>()))
           .ReturnsAsync((qcStatusesToReturn));

        // Act
        var result = await _qcPeriodService.EditPeriodAsync(qcPeriodId, qcPeriodEditRequest);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(qcPeriodId);
    }


    [Fact]
    public async Task GetAllQCPeriodsAsync_WhenCalledWithValidQCProjectId_ReturnsQCPeriodListResponse()
    {
        var qcProjectId = 123;
        var qcPeriods = new List<QCPeriod>
    {
        new QCPeriod
        {
            Id = 1,
            QCProjectId = qcProjectId,
            PeriodId = 2001,
            Status = 1,
            CreatedBy = "User1",
            CreatedWhen = DateTime.Now,
            UpdatedBy = "User1",
            UpdatedWhen = DateTime.Now
        },
        new QCPeriod
        {
            Id = 2,
            QCProjectId = qcProjectId,
            PeriodId = 2002,
            Status = 2,
            CreatedBy = "User2",
            CreatedWhen = DateTime.Now,
            UpdatedBy = "User2",
            UpdatedWhen = DateTime.Now
        }
    };

        _qcPeriodRepositoryStub.Setup(repo => repo.GetAllQCPeriodsAsync(qcProjectId)).ReturnsAsync(qcPeriods);
        Func<Task> act = async () => await _qcPeriodService.GetAllQCPeriodsAsync(qcProjectId);

        await act.Should().NotThrowAsync<EntityNotExistsException>();

        var result = await _qcPeriodService.GetAllQCPeriodsAsync(qcProjectId);

        result.Should().NotBeNull();
        result.Should().NotBeNull();
        result.Should().HaveCount(2);

    }

    [Fact]
    public async Task GetQCPeriodAsync_WhenCalledWithValidId_Returns_QCPeriodGetResponse()
    {
        // Arrange
        var userid = _fixture.Create<int>();
        var username = _fixture.Create<string>();
        var qcPeriodId = 123;
        var qcPeriod = new QCPeriodWithBPIdResponse
        {
            Id = qcPeriodId,
            QCProjectId = 456,
            Periods = _fixture.CreateMany<Period>(1).ToList()

        };

        _qcPeriodRepositoryStub.Setup(repo => repo.GetQCPeriodAsync(qcPeriodId)).ReturnsAsync(qcPeriod);

        // Act
        var result = await _qcPeriodService.GetQCPeriodAsync(qcPeriodId);

        // Assert
        result.Should().NotBeNull();
        result.Periods.Should().NotBeNull();
        result.Periods.Should().HaveCountGreaterThan(0);

    }


    [Fact]
    public async Task DeletePeriodAsync_WhenCalledWithValidIdAndRequest_ShouldSaveOutboxMessage()
    {
        // Arrange
        var qcPeriodDeleteRequest = new QCPeriodDeletes
        {
            Ids = new List<long> { 401, 402, 403 }
        };
        var qcStatusesToReturn = new List<int> { };

        var responseList = new List<ResponseInfoQCPeriod>
    {
        new ResponseInfoQCPeriod("401", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString()),
        new ResponseInfoQCPeriod("402", (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString()),
        new ResponseInfoQCPeriod("403", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString())
    };

        var qcPeriod401 = new QCPeriodWithBPIdResponse { QCProjectId = 1, Id = 401 };
        var qcPeriod402 = new QCPeriodWithBPIdResponse { QCProjectId = 2, Id = 402 };
        var qcPeriod403 = new QCPeriodWithBPIdResponse { QCProjectId = 3, Id = 403 };

        _qcPeriodRepositoryStub.Setup(repo => repo.GetQCPeriodAsync(401)).ReturnsAsync(qcPeriod401);
        _qcPeriodRepositoryStub.Setup(repo => repo.GetQCPeriodAsync(402)).ReturnsAsync(qcPeriod402);
        _qcPeriodRepositoryStub.Setup(repo => repo.GetQCPeriodAsync(403)).ReturnsAsync(qcPeriod403);
        _qcPeriodRepositoryStub.Setup(repo => repo.DeletePeriodAsync(qcPeriodDeleteRequest)).ReturnsAsync(responseList);

        _baseProjectRepositoryStub.Setup(repo => repo.GetBaseProjectWithQCStatus(It.IsAny<QCStatuses>()))
            .ReturnsAsync(qcStatusesToReturn);

        // Act
        var result = await _qcPeriodService.DeletePeriodAsync(qcPeriodDeleteRequest);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(responseList);

        // ✅ Verify that Outbox message was saved instead of sending to RabbitMQ
        _outBoxItemRepositoryStub.Verify(repo =>
            repo.SaveMessagesAsync(
                It.IsAny<ProjectServicesData>(),
                ProjectMessageType.QCPeriodDelete
            ),
            Times.Exactly(3) // Since 402 failed, only 401 and 403 should be sent
        );
    }

    // ✅ Test for Deleting All But One QCPeriod
    [Fact]
    public async Task DeletePeriodAsync_WhenDeletingAllButOne_ShouldReturnBadRequestForLastPeriod()
    {
        // Arrange
        var qcProjectId = 1;
        var idsToDelete = new List<long> { 401, 402, 403 };
        var qcPeriodDeleteRequest = new QCPeriodDeletes
        {
            Ids = idsToDelete
        };
        var qcStatusesToReturn = new List<int> { };

        var qcPeriodList = idsToDelete.Select(id => new QCPeriodWithBPIdResponse { QCProjectId = qcProjectId, Id = id }).ToList();

        var allQCPeriods = qcPeriodList.Select(q => new QCPeriod
        {
            QCProjectId = qcProjectId,
            PeriodId = q.Id
        }).ToList();

        _qcPeriodRepositoryStub.Setup(repo => repo.GetQCPeriodAsync(It.IsAny<long>()))
            .ReturnsAsync((long id) => qcPeriodList.First(q => q.Id == id));

        _qcPeriodRepositoryStub.Setup(repo => repo.GetAllQCPeriodsAsync(qcProjectId))
            .ReturnsAsync(allQCPeriods);

        _qcPeriodRepositoryStub.Setup(repo => repo.DeletePeriodAsync(It.Is<QCPeriodDeletes>(req => req.Ids.Count == 2)))
            .ReturnsAsync(idsToDelete.Take(2)
            .Select(id => new ResponseInfoQCPeriod(id.ToString(), (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString())).ToList());

        _baseProjectRepositoryStub.Setup(repo => repo.GetBaseProjectWithQCStatus(It.IsAny<QCStatuses>()))
            .ReturnsAsync(qcStatusesToReturn);

        // Act
        var result = await _qcPeriodService.DeletePeriodAsync(qcPeriodDeleteRequest);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(3);

        var lastPeriodResponse = result.Last();
        lastPeriodResponse.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
        lastPeriodResponse.StatusMsg.Should().Be($"Operation not allowed: Deleting QCPeriod {idsToDelete.Last()} would leave QCProject {qcProjectId} without any linked periods.");

        // ✅ Verify Outbox message for successful deletions
        _outBoxItemRepositoryStub.Verify(repo =>
            repo.SaveMessagesAsync(
                It.IsAny<ProjectServicesData>(),
                ProjectMessageType.QCPeriodDelete
            ),
            Times.Exactly(2) // Only first two should be sent
        );
    }


    [Fact]
    public async Task DeletePeriodAsync_WhenDeletingLastQCPeriod_ShouldReturnBadRequest()
    {
        // Arrange
        var qcProjectId = 1;
        var firstPeriodId = 401L;
        var qcPeriodDeleteRequest = new QCPeriodDeletes
        {
            Ids = new List<long> { firstPeriodId }
        };

        var qcPeriod = new QCPeriodWithBPIdResponse
        {
            QCProjectId = qcProjectId,
            PeriodId = firstPeriodId
        };

        var allQCPeriods = new List<QCPeriod>
        {
            new QCPeriod
            {
                QCProjectId = qcProjectId,
                PeriodId = firstPeriodId
            }
        };

        _qcPeriodRepositoryStub.Setup(repo => repo.GetQCPeriodAsync(firstPeriodId))
                                .ReturnsAsync(qcPeriod);

        _qcPeriodRepositoryStub.Setup(service => service.GetAllQCPeriodsAsync(qcProjectId))
                             .ReturnsAsync(allQCPeriods);

        // Act
        var result = await _qcPeriodService.DeletePeriodAsync(qcPeriodDeleteRequest);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        var response = result.First();
        response.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
        response.StatusMsg.Should().Be($"Operation not allowed: Deleting QCPeriod {firstPeriodId} would leave QCProject {qcProjectId} without any linked periods.");
    }



    [Fact]
    public async Task AddAsyncQCPeriod_WhenCalled_WithExistingPeriod_ReturnsNull()
    {
        // Arrange
        var qcPeriodCreateRequest = _fixture.Create<QCPeriod>();
        var qcPeriodCreateRequest2 = _fixture.Create<QCPeriod>();



        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(qcPeriodCreateRequest))
            .ThrowsAsync(new EntityAlreadyExistsException("A QCPeriod with the specified PeriodId already exists for this QCProjectId."));
        var result = await _qcPeriodService.AddAsyncQCPeriod(qcPeriodCreateRequest2);

        result.Should().BeNull();
    }

    [Fact]
    public async Task AutoQCPeriodCreateAsync_When_SuccessfullyCreated_Expect_SuccessfulResult()
    {
        var targetPeriodId = 12345;
        string username = "test";
        var autoQCPeriodRequest = new AutoQCPeriods
        {
            QCProjectIds = new List<int> { 1, 2 }
        };

        _operationHelper
            .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
            .ReturnsAsync((int id) => new QCPeriod { PeriodId = 111 });

        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ReturnsAsync((QCPeriod q) => new QCPeriod { Id = 999, PeriodId = targetPeriodId });

        var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

        result.Should().NotBeNull();
        result.AutoQCPeriods.Count.Should().Be(2);

        foreach (var period in result.AutoQCPeriods)
        {
            period.StatusCode.Should().Be(201);
            period.StatusMsg.Should().Be("QC period created successfully without reference periods");
            period.QCPeriodId.Should().BeGreaterThan(0);
        }
    }


    //[Fact]
    //public async Task AutoQCPeriodCreateAsync_When_SuccessfullyCreated_Expect_SuccessfulResult()
    //{
    //    // Arrange
    //    var targetPeriodId = 12345;
    //    string username = "test";
    //    var autoQCPeriodRequest = new AutoQCPeriods
    //    {
    //        QCProjectIds = new List<int> { 1, 2, 3 }
    //    };

    //    // Mock GetRecentQCPeriodsAsync to return base QC periods
    //    _operationHelper
    //        .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
    //        .ReturnsAsync((int qcProjectId) => new QCPeriod
    //        {
    //            Id = qcProjectId * 1000,
    //            PeriodId = qcProjectId * 100,
    //            Periods = new List<Period>
    //            {
    //        new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
    //        new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
    //            }
    //        });

    //    //  Mock GetShiftedPeriodsForProjectAsync with correct shifted values
    //    _operationHelper
    //        .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
    //        .ReturnsAsync((List<Period> basePeriods, int offset) => new List<ShiftedPeriod>
    //        {
    //    new ShiftedPeriod(0, 1000 + offset),
    //    new ShiftedPeriod(1, 2000 + offset)
    //        });

    //    //  Mock AddAsyncQCPeriod to simulate successful creation
    //    _qcPeriodRepositoryStub
    //        .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
    //        .ReturnsAsync((QCPeriod qcPeriod) => new QCPeriod
    //        {
    //            Id = qcPeriod.PeriodId + 5000, // Simulate new ID
    //            PeriodId = qcPeriod.PeriodId
    //        });

    //    // Act
    //    var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

    //    // Assert
    //    result.Should().NotBeNull();
    //    result.AutoQCPeriods.Should().NotBeNull();
    //    result.AutoQCPeriods.Count.Should().Be(3); // One for each QCProjectId

    //    foreach (var record in result.AutoQCPeriods)
    //    {
    //        record.QCProjectId.Should().BeGreaterThan(0);
    //        record.QCPeriodId.Should().BeGreaterThan(0);
    //        record.StatusCode.Should().Be((int)HttpStatusCode.Created); // 201
    //        record.StatusMsg.Should().Be("QC period created successfully");
    //    }
    //}


    [Fact]
    public async Task AutoQCPeriodCreateAsync_When_NoBaseQCPeriods_Expect_ErrorResult()
    {
        // Arrange
        var targetPeriodId = 12345;
        string userName = "test";
        var autoQCPeriodRequest = new AutoQCPeriods
        {
            QCProjectIds = new List<int> { 90 }
        };


        _operationHelper
            .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
            .ThrowsAsync(new EntityNotExistsException("No QCPeriod exists with Id 90"));


        _operationHelper
            .Setup(helper => helper.CalculateDistancesAsync(It.IsAny<IEnumerable<Period>>(), It.IsAny<long>()))
            .ReturnsAsync(new List<PeriodDistance>());

        _operationHelper
            .Setup(helper => helper.CalculateShiftedPeriodsAsync(It.IsAny<long>(), It.IsAny<List<PeriodDistance>>()))
            .ReturnsAsync(new List<ShiftedPeriod>());

        // Act
        var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, userName);

        // Assert
        result.Should().NotBeNull();
        result.AutoQCPeriods.Should().NotBeNull();
        result.AutoQCPeriods.Count.Should().Be(1);
        result.AutoQCPeriods.All(p => p.StatusCode == 404).Should().BeTrue();
        result.AutoQCPeriods.First().StatusMsg.Should().Be("ENTITY_NOT_EXISTS");
    }

    [Fact]
    public async Task AutoQCPeriodCreateAsync_When_BaseQCPeriodsNullOrEmpty_Expect_QCPeriodCreatedWithoutReferences()
    {
        // Arrange
        var targetPeriodId = 12345;
        string userName = "test";
        var autoQCPeriodRequest = new AutoQCPeriods
        {
            QCProjectIds = new List<int> { 90 }
        };

        var baseQCPeriod = new QCPeriod
        {
            QCProjectId = 90,
            PeriodId = targetPeriodId,
            Periods = null // Simulate null Periods
        };

        _operationHelper
            .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
            .ReturnsAsync(baseQCPeriod);

        _qcPeriodRepositoryStub
            .Setup(helper => helper.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ReturnsAsync(new QCPeriod
            {
                Id = 999,
                PeriodId = targetPeriodId,
                QCProjectId = 90,
                Periods = new List<Period>(),
                StockInitialization = new StockInitialization { QCPeriodId = 90, StockBaseProjectId = null, StockPeriodId = null }
            });

        // Act
        var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, userName);

        // Assert
        result.Should().NotBeNull();
        result.AutoQCPeriods.Should().NotBeNull();
        result.AutoQCPeriods.Count.Should().Be(1);
        result.AutoQCPeriods.First().StatusCode.Should().Be((int)HttpStatusCode.Created);
        result.AutoQCPeriods.First().StatusMsg.Should().Be("QC period created successfully without reference periods");
        result.AutoQCPeriods.First().QCPeriodId.Should().Be(999);
    }

    [Fact]
    public async Task AutoQCPeriodCreateAsync_When_QCPeriodAlreadyexists_Expect_ErrorResult()
    {
        var targetPeriodId = 12345;
        string username = "test";
        var autoQCPeriodRequest = new AutoQCPeriods
        {
            QCProjectIds = new List<int> { 90 }
        };

        _operationHelper
            .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
            .ReturnsAsync(new QCPeriod { PeriodId = 111 });

        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new EntityAlreadyExistsException("A QCPeriod with the specified PeriodId already exists for this QCProjectId."));

        var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

        result.Should().NotBeNull();
        result.AutoQCPeriods.Count.Should().Be(1);
        result.AutoQCPeriods.First().StatusCode.Should().Be(400);
        result.AutoQCPeriods.First().StatusMsg.Should().Be("DUPLICATION_ENTITY");
    }


    //[Fact]
    //public async Task AutoQCPeriodCreateAsync_When_QCPeriodAlreadyexists_Expect_ErrorResult()
    //{
    //    // Arrange
    //    var targetPeriodId = 12345;
    //    string username = "test";
    //    var autoQCPeriodRequest = new AutoQCPeriods
    //    {
    //        QCProjectIds = new List<int> { 90 }
    //    };

    //    // Mock GetRecentQCPeriodsAsync to return a QCPeriod with existing periods
    //    _operationHelper
    //        .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
    //        .ReturnsAsync((int qcProjectId) => new QCPeriod
    //        {
    //            Id = qcProjectId * 1000,
    //            PeriodId = qcProjectId * 100,
    //            Periods = new List<Period>
    //            {
    //        new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
    //        new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
    //            }
    //        });

    //    //  Mock GetShiftedPeriodsForProjectAsync (correct method used now)
    //    _operationHelper
    //        .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
    //        .ReturnsAsync(new List<ShiftedPeriod>
    //        {
    //    new ShiftedPeriod(0, 1001),
    //    new ShiftedPeriod(1, 1002)
    //        });

    //    // Mock AddAsyncQCPeriod to throw EntityAlreadyExistsException
    //    _qcPeriodRepositoryStub
    //        .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
    //        .ThrowsAsync(new EntityAlreadyExistsException("A QCPeriod with the specified PeriodId already exists for this QCProjectId."));

    //    // Act
    //    var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

    //    // Assert
    //    result.Should().NotBeNull();
    //    result.AutoQCPeriods.Should().NotBeNull();
    //    result.AutoQCPeriods.Count.Should().Be(1);
    //    result.AutoQCPeriods.All(p => p.StatusCode == 400).Should().BeTrue();
    //    result.AutoQCPeriods.First().StatusMsg.Should().Be("DUPLICATION_ENTITY");
    //}

    [Fact]
    public async Task AutoQCPeriodCreateAsync_When_HttpRequestException_Expect_InternalServerErrorResult()
    {
        var targetPeriodId = 12345;
        string username = "test";
        var autoQCPeriodRequest = new AutoQCPeriods
        {
            QCProjectIds = new List<int> { 90 }
        };

        _operationHelper
            .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
            .ReturnsAsync(new QCPeriod { PeriodId = 111 });

        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new HttpRequestException("Network error"));

        var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

        result.Should().NotBeNull();
        result.AutoQCPeriods.Count.Should().Be(1);
        result.AutoQCPeriods.First().StatusCode.Should().Be(500);
        result.AutoQCPeriods.First().StatusMsg.Should().Be("Network error");
    }


    //[Fact]
    //public async Task AutoQCPeriodCreateAsync_When_HttpRequestException_Expect_InternalServerErrorResult()
    //{
    //    // Arrange
    //    var targetPeriodId = 12345;
    //    string username = "test";
    //    var autoQCPeriodRequest = new AutoQCPeriods
    //    {
    //        QCProjectIds = new List<int> { 90 }
    //    };

    //    // Mock GetRecentQCPeriodsAsync to return a QCPeriods
    //    _operationHelper
    //        .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
    //        .ReturnsAsync((int qcProjectId) => new QCPeriod
    //        {
    //            Id = qcProjectId * 1000,
    //            PeriodId = qcProjectId * 100,
    //            Periods = new List<Period>
    //            {
    //        new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
    //        new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
    //            }
    //        });

    //    //  Mock GetShiftedPeriodsForProjectAsync (not CalculateDistancesAsync / CalculateShiftedPeriodsAsync anymore)
    //    _operationHelper
    //        .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
    //        .ReturnsAsync(new List<ShiftedPeriod>
    //        {
    //    new ShiftedPeriod(0, 1001),
    //    new ShiftedPeriod(1, 1002)
    //        });

    //    // Mock AddAsyncQCPeriod to throw HttpRequestException
    //    _qcPeriodRepositoryStub
    //        .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
    //        .ThrowsAsync(new HttpRequestException("Network error"));

    //    // Act
    //    var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

    //    // Assert
    //    result.Should().NotBeNull();
    //    result.AutoQCPeriods.Should().NotBeNull();
    //    result.AutoQCPeriods.Count.Should().Be(1);
    //    result.AutoQCPeriods.All(p => p.StatusCode == 500).Should().BeTrue();
    //    result.AutoQCPeriods.First().StatusMsg.Should().Be("Network error");
    //}

    [Fact]
    public async Task AutoQCPeriodCreateAsync_When_GeneralException_Expect_InternalServerErrorResult()
    {
        var targetPeriodId = 12345;
        string username = "test";
        var autoQCPeriodRequest = new AutoQCPeriods
        {
            QCProjectIds = new List<int> { 90 }
        };

        _operationHelper
            .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
            .ReturnsAsync(new QCPeriod { PeriodId = 111 });

        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new Exception("Unexpected error"));

        var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

        result.Should().NotBeNull();
        result.AutoQCPeriods.Count.Should().Be(1);
        result.AutoQCPeriods.First().StatusCode.Should().Be(500);
        result.AutoQCPeriods.First().StatusMsg.Should().Be("Unexpected error: Unexpected error");
    }


    //[Fact]
    //public async Task AutoQCPeriodCreateAsync_When_GeneralException_Expect_InternalServerErrorResult()
    //{
    //    // Arrange
    //    var targetPeriodId = 12345;
    //    string username = "test";
    //    var autoQCPeriodRequest = new AutoQCPeriods
    //    {
    //        QCProjectIds = new List<int> { 90 }
    //    };

    //    // Mock GetRecentQCPeriodsAsync to return a QCPeriods
    //    _operationHelper
    //        .Setup(helper => helper.GetRecentQCPeriodsAsync(It.IsAny<int>()))
    //        .ReturnsAsync((int qcProjectId) => new QCPeriod
    //        {
    //            Id = qcProjectId * 1000,
    //            PeriodId = qcProjectId * 100,
    //            Periods = new List<Period>
    //            {
    //        new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
    //        new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
    //            }
    //        });

    //    //  NEW: Mock GetShiftedPeriodsForProjectAsync instead of CalculateDistancesAsync + CalculateShiftedPeriodsAsync
    //    _operationHelper
    //        .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
    //        .ReturnsAsync(new List<ShiftedPeriod>
    //        {
    //    new ShiftedPeriod(0, 1001),
    //    new ShiftedPeriod(1, 1002)
    //        });

    //    // Mock AddAsyncQCPeriod to throw a general Exception
    //    _qcPeriodRepositoryStub
    //        .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
    //        .ThrowsAsync(new Exception("Unexpected error"));

    //    // Act
    //    var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, autoQCPeriodRequest, username);

    //    // Assert
    //    result.Should().NotBeNull();
    //    result.AutoQCPeriods.Should().NotBeNull();
    //    result.AutoQCPeriods.Count.Should().Be(1);
    //    result.AutoQCPeriods.All(p => p.StatusCode == 500).Should().BeTrue();
    //    result.AutoQCPeriods.First().StatusMsg.Should().Be("Unexpected error: Unexpected error");
    //}

    /// <summary>
    /// ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /// </summary>
    /// <returns></returns>

    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_SuccessfullyCreated_Expect_SuccessfulResult()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock base QC period
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                PeriodId = bulkQCPeriodRange.EndPeriod,
                Periods = new List<Period>
                {
                    new Period { index = 0, RefPeriodId = 111, RefProjectId = 1 },
                    new Period { index = 1, RefPeriodId = 222, RefProjectId = 1 }
                }
            });

        // Mock period short names
        _operationHelper
            .Setup(helper => helper.GetPeriodShortNameAsync(It.IsAny<long>()))
            .ReturnsAsync("Jan23");

        // Mock periodicity ID
        _operationHelper
            .Setup(helper => helper.GetPeriodicityIdAsync(It.IsAny<int>()))
            .ReturnsAsync(4);

        // Mock GetPeriodRangeAsync
        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302, 202303 });

        // 🔥 Mock new function correctly
        _operationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 2001),
                new ShiftedPeriod(1, 2002)
            });

        // Mock AddAsyncQCPeriod
        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ReturnsAsync((QCPeriod qcPeriod) => new QCPeriod
            {
                Id = 999,
                PeriodId = qcPeriod.PeriodId
            });

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeEmpty();

        foreach (var period in result.BulkQCPeriods)
        {
            period.QCProjectId.Should().Be(qcProjectId);
            period.QCPeriodId.Should().BeGreaterThan(0);
            period.StatusCode.Should().Be((int)HttpStatusCode.Created);
            period.StatusMsg.Should().Be("QC periods created successfully");
        }
    }
    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_SuccessfullyCreated_Expect_SuccessfulResultWeekly()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock GetBaseQCPeriodAsync to return base QC period
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                PeriodId = bulkQCPeriodRange.EndPeriod,
                Periods = new List<Period>
                {
                    new Period { index = 0, RefPeriodId = 111, RefProjectId = 1 },
                    new Period { index = 1, RefPeriodId = 222, RefProjectId = 1 }
                }
            });

        // Mock GetPeriodShortNameAsync to return short names
        _operationHelper
            .Setup(helper => helper.GetPeriodShortNameAsync(It.IsAny<long>()))
            .ReturnsAsync("Jan23");

        // Mock GetPeriodicityIdAsync to return Weekly periodicity (2)
        _operationHelper
            .Setup(helper => helper.GetPeriodicityIdAsync(It.IsAny<int>()))
            .ReturnsAsync(2); // 2 => Weekly

        // Mock GetPeriodRangeAsync
        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302, 202303 });

        // Mock GetShiftedPeriodsForProjectAsync
        _operationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 2001),
                new ShiftedPeriod(1, 2002)
            });

        // Mock AddAsyncQCPeriod to simulate successful addition
        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ReturnsAsync((QCPeriod qcPeriod) => new QCPeriod
            {
                Id = 999,
                PeriodId = qcPeriod.PeriodId
            });

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();

        foreach (var period in result.BulkQCPeriods)
        {
            period.QCProjectId.Should().Be(qcProjectId);
            period.QCPeriodId.Should().BeGreaterThan(0);
            period.StatusCode.Should().Be((int)HttpStatusCode.Created);
            period.StatusMsg.Should().Be("QC periods created successfully");
        }
    }


    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_SuccessfullyCreated_Expect_SuccessfulResultDaily()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                PeriodId = bulkQCPeriodRange.EndPeriod,
                Periods = new List<Period>
                {
                    new Period { index = 0, RefPeriodId = 111, RefProjectId = 1 },
                    new Period { index = 1, RefPeriodId = 222, RefProjectId = 1 }
                }
            });

        _operationHelper
            .Setup(helper => helper.GetPeriodShortNameAsync(It.IsAny<long>()))
            .ReturnsAsync("Jan23");

        _operationHelper
            .Setup(helper => helper.GetPeriodicityIdAsync(It.IsAny<int>()))
            .ReturnsAsync(1); // 1 => Daily

        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302, 202303 });

        _operationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 2001),
                new ShiftedPeriod(1, 2002)
            });

        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ReturnsAsync((QCPeriod qcPeriod) => new QCPeriod
            {
                Id = 999,
                PeriodId = qcPeriod.PeriodId
            });

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();

        foreach (var period in result.BulkQCPeriods)
        {
            period.QCProjectId.Should().Be(qcProjectId);
            period.QCPeriodId.Should().BeGreaterThan(0);
            period.StatusCode.Should().Be((int)HttpStatusCode.Created);
            period.StatusMsg.Should().Be("QC periods created successfully");
        }
    }
    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_SuccessfullyCreated_Expect_SuccessfulResultOther()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                PeriodId = bulkQCPeriodRange.EndPeriod,
                Periods = new List<Period>
                {
                    new Period { index = 0, RefPeriodId = 111, RefProjectId = 1 },
                    new Period { index = 1, RefPeriodId = 222, RefProjectId = 1 }
                }
            });

        _operationHelper
            .Setup(helper => helper.GetPeriodShortNameAsync(It.IsAny<long>()))
            .ReturnsAsync("Jan23");

        _operationHelper
            .Setup(helper => helper.GetPeriodicityIdAsync(It.IsAny<int>()))
            .ReturnsAsync(5); // 5 => Other periodicity type

        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302, 202303 });

        _operationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 2001),
                new ShiftedPeriod(1, 2002)
            });

        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ReturnsAsync((QCPeriod qcPeriod) => new QCPeriod
            {
                Id = 999,
                PeriodId = qcPeriod.PeriodId
            });

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Should().HaveCountGreaterThan(0);

        foreach (var record in result.BulkQCPeriods)
        {
            record.QCProjectId.Should().Be(qcProjectId);
            record.QCPeriodId.Should().BeGreaterThan(0);
            record.StatusCode.Should().Be((int)HttpStatusCode.Created);
            record.StatusMsg.Should().Be("QC periods created successfully");
        }
    }


    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_EntityAlreadyExistsExceptionThrown_Expect_BadRequestStatus()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock GetBaseQCPeriodAsync to return a baseQCPeriod
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                Id = qcProjectId * 1000,
                PeriodId = qcProjectId * 100,
                Periods = new List<Period>
                {
                    new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
                    new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
                }
            });

        // Mock GetPeriodRangeAsync to return a range of periods
        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302 });

        // Mock CalculateShiftedPeriodsPerProjectAsync to return a list of shifted periods
        _operationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 1001),
                new ShiftedPeriod(1, 1002)
            });

        // Mock AddAsyncQCPeriod to throw EntityAlreadyExistsException
        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new EntityAlreadyExistsException("A QCPeriod with the specified PeriodId already exists"));

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Count.Should().Be(2); // Expecting 2 records based on the logic

        foreach (var record in result.BulkQCPeriods)
        {
            record.StatusCode.Should().Be(400); // Bad Request status code
            record.StatusMsg.Should().Contain("DUPLICATION_ENTITY"); // Error message should indicate duplication
        }
    }

    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_HttpRequestExceptionThrown_Expect_InternalServerErrorStatus()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock GetBaseQCPeriodAsync to return a baseQCPeriod
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                Id = qcProjectId * 1000,
                PeriodId = qcProjectId * 100,
                Periods = new List<Period>
                {
                    new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
                    new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
                }
            });

        // Mock GetPeriodRangeAsync to return a range of periods
        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302 });

        // Mock CalculateShiftedPeriodsPerProjectAsync to return shifted periods (updated function)
        _operationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 1001),
                new ShiftedPeriod(1, 1002)
            });

        // Mock AddAsyncQCPeriod to throw an HttpRequestException
        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new HttpRequestException("Network error"));

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Count.Should().Be(2); // We expect 2 records based on logic

        foreach (var record in result.BulkQCPeriods)
        {
            record.StatusCode.Should().Be(500); // Internal Server Error status code
            record.StatusMsg.Should().Contain("Network error"); // Error message indicating a network issue
        }
    }


    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_GeneralExceptionThrown_Expect_InternalServerErrorStatus()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock GetBaseQCPeriodAsync to return a baseQCPeriod
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                Id = qcProjectId * 1000,
                PeriodId = qcProjectId * 100,
                Periods = new List<Period>
                {
            new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
            new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
                }
            });

        // Mock GetPeriodRangeAsync to return a range of periods
        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302 });

        // Mock CalculateDistancesAsync to return a list of distances
        _operationHelper
            .Setup(helper => helper.CalculateDistancesAsync(It.IsAny<IEnumerable<Period>>(), It.IsAny<long>()))
            .ReturnsAsync(new List<PeriodDistance>
            {
        new PeriodDistance(0, 10),
        new PeriodDistance(1, 20)
            });

        // Mock CalculateShiftedPeriodsAsync to return a list of shifted periods
        _operationHelper
            .Setup(helper => helper.CalculateShiftedPeriodsAsync(It.IsAny<long>(), It.IsAny<List<PeriodDistance>>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
        new ShiftedPeriod(0, 1001),
        new ShiftedPeriod(1, 1002)
            });

        // Mock AddAsyncQCPeriod to throw a general Exception
        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new Exception("Unexpected error"));

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Count.Should().Be(2);

        foreach (var record in result.BulkQCPeriods)
        {
            record.StatusCode.Should().Be(500); // Internal Server Error status code
            record.StatusMsg.Should().Contain("Unexpected error"); // Error message should indicate an unexpected error
        }
    }

    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_EntityNotExistsExceptionThrown_Expect_NotFoundStatus()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock GetBaseQCPeriodAsync to throw EntityNotExistsException
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ThrowsAsync(new EntityNotExistsException("No QCPeriods exist"));

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Count.Should().Be(1);

    }

    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_EntityNotExistsExceptionThrownInProcessQCPeriod_Expect_NotFoundStatus()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock GetBaseQCPeriodAsync to return a baseQCPeriod
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                Id = qcProjectId * 1000,
                PeriodId = qcProjectId * 100,
                Periods = new List<Period>
                {
                    new Period { RefPeriodId = 111, RefProjectId = 222, index = 0 },
                    new Period { RefPeriodId = 333, RefProjectId = 444, index = 1 }
                }
            });

        // Mock GetPeriodRangeAsync to return a range of periods
        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302 });

        // Mock CalculateShiftedPeriodsPerProjectAsync to return shifted periods (updated function)
        _operationHelper
            .Setup(helper => helper.GetShiftedPeriodsForProjectAsync(It.IsAny<List<Period>>(), It.IsAny<int>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 1001),
                new ShiftedPeriod(1, 1002)
            });

        // Mock AddAsyncQCPeriod to throw an EntityNotExistsException
        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new EntityNotExistsException("QC Period does not exist"));

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Count.Should().Be(2); // We expect 2 records based on logic

        foreach (var record in result.BulkQCPeriods)
        {
            record.StatusCode.Should().Be(404); // Not Found status code
            record.StatusMsg.Should().Contain("ENTITY_NOT_EXISTS"); // Error message indicating the entity doesn't exist
        }
    }
    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_GenericExceptionThrown_Expect_InternalServerErrorStatus()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "test";

        // Mock GetBaseQCPeriodAsync to throw a generic exception
        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ThrowsAsync(new Exception("Generic exception"));

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Count.Should().Be(1);

        result.BulkQCPeriods.Should().ContainSingle(record =>
            record.StatusCode == (int)HttpStatusCode.InternalServerError &&
            record.StatusMsg == "Unexpected error: Generic exception"
        );
    }

    [Fact]
    public async Task DeletePeriodAsync_WhenDeletingAllButOne_ShouldReturnBadRequestForQCStatus()
    {
        // Arrange
        var qcProjectId = 1;
        var idsToDelete = new List<long> { 401, 402 };
        var qcPeriodDeleteRequest = new QCPeriodDeletes
        {
            Ids = idsToDelete
        };
        var qcStatusesToReturn = new List<int> { 1401 };

        var qcPeriodList = idsToDelete.Select(id => new QCPeriodWithBPIdResponse { QCProjectId = qcProjectId, Id = id }).ToList();

        var allQCPeriods = qcPeriodList.Select(q => new QCPeriod
        {
            QCProjectId = qcProjectId,
            PeriodId = q.PeriodId
        }).ToList();

        _qcPeriodRepositoryStub.Setup(repo => repo.GetQCPeriodAsync(It.IsAny<long>()))
                                .ReturnsAsync((long id) => qcPeriodList.First(q => q.Id == id));

        _qcPeriodRepositoryStub.Setup(service => service.GetAllQCPeriodsAsync(qcProjectId))
                             .ReturnsAsync(allQCPeriods);

        _baseProjectRepositoryStub.Setup(repo => repo.GetBaseProjectWithQCStatus(It.IsAny<QCStatuses>()))
            .ReturnsAsync((qcStatusesToReturn));

        // Act
        Func<Task> act = async () => await _qcPeriodService.DeletePeriodAsync(qcPeriodDeleteRequest);

        // Assert
        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage($"QC Period(s) cannot be deleted as they are currently in QC Status");
    }

    [Fact]
    public async Task EditQCPeriodAsync_WhenCalled_WithQCStatus_Throws_Exception()
    {
        // Arrange
        var qcPeriodId = 1;
        var qcPeriodEditRequest = _fixture.Create<QCPeriodEdits>();
        var qcStatusesToReturn = new List<int> { 1 };
        var existingPeriods = _fixture.Create<QCPeriodWithBPIdResponse>();
        existingPeriods.Id = qcPeriodId;

        _qcPeriodRepositoryStub
            .Setup(repo => repo.GetQCPeriodAsync(qcPeriodId))
            .ReturnsAsync(existingPeriods);
        _qcPeriodRepositoryStub
            .Setup(repo => repo.EditPeriodAsync(qcPeriodId, qcPeriodEditRequest))
            .ThrowsAsync(new EntityNotExistsException($"No Period exists with Id {qcPeriodId}"));
        _baseProjectRepositoryStub.Setup(repo => repo.GetBaseProjectWithQCStatus(It.IsAny<QCStatuses>()))
           .ReturnsAsync((qcStatusesToReturn));

        // Act
        Func<Task> act = async () => await _qcPeriodService.EditPeriodAsync(qcPeriodId, qcPeriodEditRequest);

        // Assert
        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage($"QC Period cannot be edited as it is currently in QC Status");
    }

    [Fact]
    public async Task BulkQCPeriodCreateAsync_When_AddAsyncQCPeriodThrowsException_Expect_InternalServerError()
    {
        // Arrange
        var qcProjectId = 123;
        var bulkQCPeriodRange = new BulkQCPeriods
        {
            StartPeriod = 202301,
            EndPeriod = 202312
        };
        var userName = "testUser";

        _operationHelper
            .Setup(helper => helper.GetBaseQCPeriodAsync(It.IsAny<int>(), It.IsAny<long>()))
            .ReturnsAsync(new QCPeriod
            {
                PeriodId = bulkQCPeriodRange.EndPeriod,
                Periods = new List<Period>
                {
            new Period { index = 0, RefPeriodId = 111, RefProjectId = 1 },
            new Period { index = 1, RefPeriodId = 222, RefProjectId = 1 }
                }
            });

        _operationHelper
            .Setup(helper => helper.GetPeriodShortNameAsync(It.IsAny<long>()))
            .ReturnsAsync("Jan23");

        _operationHelper
            .Setup(helper => helper.GetPeriodicityIdAsync(It.IsAny<int>()))
            .ReturnsAsync(5); // Other periodicity

        _operationHelper
            .Setup(helper => helper.GetPeriodRangeAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new HashSet<long> { 202301, 202302 });

        _operationHelper
            .Setup(helper => helper.CalculateShiftedPeriodsAsync(It.IsAny<int>(), It.IsAny<List<PeriodDistance>>()))
            .ReturnsAsync(new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 2001),
                new ShiftedPeriod(1, 2002)
            });

        // 🛑 Force AddAsyncQCPeriod to throw an unexpected Exception
        _qcPeriodRepositoryStub
            .Setup(repo => repo.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
            .ThrowsAsync(new Exception("Simulated Database Failure"));

        // Act
        var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, bulkQCPeriodRange, userName);

        // Assert
        result.Should().NotBeNull();
        result.BulkQCPeriods.Should().NotBeNull();
        result.BulkQCPeriods.Should().HaveCountGreaterThan(0);

        foreach (var record in result.BulkQCPeriods)
        {
            record.QCProjectId.Should().Be(qcProjectId);
            record.StatusCode.Should().Be((int)HttpStatusCode.InternalServerError);
            record.StatusMsg.Should().Contain("Unexpected error:");
        }
    }
}
