﻿using AutoFixture;
using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Presentation.Profile;
using DWH.ProjectServices.API.Services;
using FluentAssertions;
using Moq;
using Xunit.Extensions.AssertExtensions;

namespace DWH.ProjectServices.API.UnitTests.Services;

public class QCProjectServiceTests
{

    private readonly IFixture _fixture;
    private readonly IMapper _mapper;
    private readonly Mock<IQCProjectRepository> _qcProjectRepositoryStub;
    private readonly QCProjectService _qcProjectService;
    private readonly Mock<IOutBoxItemRepository> _mockoutBoxItemRepository;

    public QCProjectServiceTests()
    {
        _fixture = new Fixture();
        var mappingConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new Infrastructure.Persistence.Profile.QCProjectProfile());
            mc.AddProfile(new QCProjectandPeriodProfile());
        });
        _mapper = mappingConfig.CreateMapper();
        _qcProjectRepositoryStub = new Mock<IQCProjectRepository>();
        _mockoutBoxItemRepository = new Mock<IOutBoxItemRepository>();

        _qcProjectService = new QCProjectService(_qcProjectRepositoryStub.Object, _mockoutBoxItemRepository.Object);
    }
    [Fact]
    public async Task UpdateAsync_WhenCalled_WithInvalidIdInModel_Throws_Exception()
    {
        // Arrange
        var baseProjectId = 1;
        var qcProjectEditRequest = _fixture.Create<QCProjectUpdates>();

        _qcProjectRepositoryStub.Setup(b => b.UpdateAsync(baseProjectId, qcProjectEditRequest))
                                 .ThrowsAsync(new EntityNotExistsException($"No QC Project exists with Id {baseProjectId}",
                                                                           "QC Project", baseProjectId));

        // Act
        Func<Task> act = async () => await _qcProjectService.UpdateAsync(baseProjectId, qcProjectEditRequest);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>()
                  .WithMessage($"ENTITY_NOT_EXISTS");
    }




    [Fact]
    public async void UpdateAsync_WhenCalled_WithValidModel_Updates_Successfully()
    {
        var baseProjectId = 1;
        var baseProjectEditRequest = _fixture.Create<QCProjectUpdates>();

        var expectedResultRepository = new QCProject();
        expectedResultRepository.Id = baseProjectId;
        var expectedResultService = new QCProjectEditResponse();
        expectedResultService.Id = baseProjectId;

        _qcProjectRepositoryStub.Setup(b => b.UpdateAsync(baseProjectId, baseProjectEditRequest)).ReturnsAsync(expectedResultRepository);

        var result = await _qcProjectService.UpdateAsync(baseProjectId, baseProjectEditRequest);

        result.Id.ShouldEqual(expectedResultService.Id);
    }

    [Fact]
    public async Task GetAsync_When_RepositoryReturnsNull_Expect_EmptyNull()
    {
        // Arrange
        List<int> expectedResult = new List<int> { };
        var qcProjectCountryRequest = new QCProjectCountryRequest
        {
        };
        var qcProjectCountryModel = _mapper.Map<QCProjectCountries>(qcProjectCountryRequest);

        _qcProjectRepositoryStub
            .Setup(r => r.GetByQCProjectIdAsync(qcProjectCountryModel))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _qcProjectService.GetAsync(qcProjectCountryModel);
        result.Should().HaveCount(0);
    }

    [Fact]
    public async Task GetAsync_When_RepositoryReturnsNonEmptyList_Expect_SameList()
    {
        // Arrange
        var expectedResult = new List<int> { 1, 2, 3 };
        var qcProjectCountryRequest = new QCProjectCountryRequest
        {
            QCProjectIds = new int[] { 1, 2, 3 },
            CountryIds = new int[] { 15 }
        };
        var qcProjectCountryModel = _mapper.Map<QCProjectCountries>(qcProjectCountryRequest);

        _qcProjectRepositoryStub
            .Setup(r => r.GetByQCProjectIdAsync(qcProjectCountryModel))
            .ReturnsAsync(expectedResult);


        // Act
        var result = await _qcProjectService.GetAsync(qcProjectCountryModel);

        // Assert
        result.Should().BeEquivalentTo(expectedResult);
    }


}
