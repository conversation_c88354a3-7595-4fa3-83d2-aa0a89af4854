﻿using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;

public class OutBoxItemRepository : IOutBoxItemRepository
{
    private readonly PostgreSqlDbContext _postdbContext;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public OutBoxItemRepository(PostgreSqlDbContext postdbContext, IServiceScopeFactory serviceScopeFactory)
    {
        _postdbContext = postdbContext;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task SaveMessagesAsync(object message, ProjectMessageType messageTypeId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
        var outBoxItem = new OutBoxItemEntity
        {
            Payload = System.Text.Json.JsonSerializer.Serialize(message),
            TypeId = messageTypeId.ToString(),
            Status = OutboxStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        dbContext.Add(outBoxItem);
        await dbContext.SaveChangesAsync();
    }

    public async Task<List<OutBoxItemEntity>> GetUnprocessedMessagesAsync()
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
        return await dbContext.OutBoxItem
            .Where(m => m.Status == OutboxStatus.Pending)
            .OrderBy(m => m.CreatedAt)
            .ToListAsync();
    }

    public async Task MarkMessageAsProcessedAsync(Guid messageId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
        var message = await dbContext.OutBoxItem.FindAsync(messageId);
        if (message != null)
        {
            message.Status = OutboxStatus.Processed;
            message.ProcessedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();
        }
    }
}
