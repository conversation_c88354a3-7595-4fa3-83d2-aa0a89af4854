﻿using System.Net;
using AutoFixture;
using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit.Extensions.AssertExtensions;

namespace DWH.ProjectServices.API.UnitTests.Services;

public class RetailerSeperationServiceTests
{
    private readonly IFixture _fixture;
    private readonly IMapper _mapper;
    private readonly Mock<ILogger<RetailerSeperationService>> _loggerStub;
    private readonly Mock<IRetailerSeperationRepository> _retailerSeperationRepositoryStub;
    private readonly IRetailerSeperationService _service;
    private readonly Mock<IBaseProjectRepository> _baseProjectRepositoryStub;
    private readonly Mock<IJiraHelper> _jiraHelper;
    private readonly Mock<IConfiguration> _configuration;
    private readonly Mock<ISecurityHelper> _securityHelper;
    private readonly Mock<IOutBoxItemRepository> _mockoutBoxItemRepository;

    public RetailerSeperationServiceTests()
    {
        _fixture = new Fixture();
        var mappingConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new Infrastructure.Persistence.Profile.RetailerSeperationProfile());
        });
        _mapper = mappingConfig.CreateMapper();
        _loggerStub = new Mock<ILogger<RetailerSeperationService>>();
        _retailerSeperationRepositoryStub = new Mock<IRetailerSeperationRepository>();
        _baseProjectRepositoryStub = new Mock<IBaseProjectRepository>();
        _jiraHelper = new Mock<IJiraHelper>();
        _configuration = new Mock<IConfiguration>();
        _securityHelper = new Mock<ISecurityHelper>();
        _mockoutBoxItemRepository = new Mock<IOutBoxItemRepository>();

        _baseProjectRepositoryStub = new Mock<IBaseProjectRepository>();
        _service = new RetailerSeperationService(_mapper, _retailerSeperationRepositoryStub.Object,
           _loggerStub.Object, _jiraHelper.Object, _mockoutBoxItemRepository.Object, _configuration.Object,
           _baseProjectRepositoryStub.Object, _securityHelper.Object);
    }

    [Fact]
    public async Task AddAsync_When_SuccessfullyAdded_Expect_SuccessfulResult()
    {
        // Arrange
        var email = _fixture.Create<string>();
        var origin = _fixture.Create<string>();
        var retailerSeperation = _fixture.Build<RetailerSeperationRequest>()
            .With(rs => rs.RetailerSeperations, _fixture.CreateMany<RetailerSeperation>(20).ToList())
            .Create();

        var expectedJiraTicket = new JiraTicket
        {
            name = _fixture.Create<string>()
        };

        _jiraHelper
            .Setup(helper => helper.PostTicket(It.IsAny<CreateTicketDetails>()))
            .ReturnsAsync(expectedJiraTicket);

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.AddAsync(retailerSeperation))
            .ReturnsAsync(retailerSeperation);

        // Act
        var result = await _service.AddAsync(retailerSeperation, email);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(retailerSeperation);
    }

    [Fact]
    public async Task AddAsync_When_Morethan20SourceBPs_Expect_UnSuccessfulResult()
    {
        // Arrange
        var email = _fixture.Create<string>();
        var origin = _fixture.Create<string>();
        var retailerSeperation = _fixture.Build<RetailerSeperationRequest>()
            .With(rs => rs.RetailerSeperations, _fixture.CreateMany<RetailerSeperation>(21).ToList())
            .Create();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.AddAsync(retailerSeperation))
            .ReturnsAsync(retailerSeperation);

        // Act
        Func<Task> act = async () => await _service.AddAsync(retailerSeperation, email);

        // Assert
        await act.Should()
            .ThrowAsync<InvalidOperationException>()
            .WithMessage("You cannot add more than 20 base projects in a single I/R request.");
    }


    //[Fact]
    //public async Task AddAsync_When_JiraFailed_Expect_UnSuccessfulResult()
    //{
    //    // Arrange
    //    var email = _fixture.Create<string>();
    //    var origin = _fixture.Create<string>();
    //    var retailerSeperation = _fixture.Build<RetailerSeperationRequests>()
    //        .With(rs => rs.RetailerSeperations, _fixture.CreateMany<RetailerSeperations>(20).ToList())
    //        .Create();

    //    _operationHelper
    //       .Setup(helper => helper.PostJiraTicket(It.IsAny<string>(), It.IsAny<int[]>(), It.IsAny<string>()))
    //       .ReturnsAsync((JiraTicket)null);


    //    _retailerSeperationRepositoryStub
    //        .Setup(repo => repo.AddAsync(retailerSeperation))
    //        .ReturnsAsync(retailerSeperation);

    //    // Act
    //    Func<Task> act = async () => await _service.AddAsync(retailerSeperation, email, origin);

    //    // Assert
    //    await act.Should()
    //        .ThrowAsync<BadHttpRequestException>()
    //        .WithMessage("Jira Ticket Creation Failed. ");
    //}

    [Fact]
    public async Task GetAsyncList_When_ListRetrievedSuccessfully_Expect_SuccessfulResult()
    {
        // Arrange
        var retailerSeperationLists = _fixture.Create<RetailerSeperationsLists>();
        var retailerSeperationEntities = _fixture.CreateMany<RetailerSeperationRequest>().ToList();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsyncList(It.IsAny<RetailerSeperationsLists>()))
            .ReturnsAsync(retailerSeperationEntities);

        // Act
        var result = await _service.GetAsyncList(retailerSeperationLists);

        // Assert
        result.Should().NotBeNull();
        result.Counts.Should().Be(retailerSeperationEntities.Count);
        result.Records.Should().BeEquivalentTo(_mapper.Map<ICollection<RetailerSeperationResponse>>(retailerSeperationEntities));
    }

    [Fact]
    public async Task GetAsyncList_When_EmptyListRetrieved_Expect_EmptyResult()
    {
        // Arrange
        var retailerSeperationLists = _fixture.Create<RetailerSeperationsLists>();
        var emptyRetailerSeperationEntities = new List<RetailerSeperationRequest>();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsyncList(It.IsAny<RetailerSeperationsLists>()))
            .ReturnsAsync(emptyRetailerSeperationEntities);

        // Act
        var result = await _service.GetAsyncList(retailerSeperationLists);

        // Assert
        result.Should().NotBeNull();
        result.Counts.Should().Be(0);
        result.Records.Should().BeEmpty();
    }

    [Fact]
    public async Task GetUsersList_When_UsersRetrievedSuccessfully_Expect_SuccessfulResult()
    {
        // Arrange
        var usersList = _fixture.Create<IReadOnlyList<string>>();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetUsersList())
            .ReturnsAsync(usersList);

        // Act
        var result = await _service.GetUsersList();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(usersList);
    }

    [Fact]
    public async Task UpdateAsync_When_Successful_Expect_SuccessfulResult()
    {
        // Arrange
        var sbpId = 1;
        var username = _fixture.Create<string>();
        var userEmail = _fixture.Create<string>();
        var errorDetails = _fixture.Create<string>();
        var retailerSeperation = _fixture.Build<RetailerSeperationRequest>()
            .With(rs => rs.RetailerSeperations, _fixture.CreateMany<RetailerSeperation>().ToList())
        .Create();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsync(It.IsAny<int>()))
            .ReturnsAsync(retailerSeperation);

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAuthorizedBaseProjectsByCountry(It.IsAny<RetailerSeparationCountries>()))
            .ReturnsAsync(new List<int> { sbpId });

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.UpdateAsync(username, retailerSeperation))
            .ReturnsAsync(retailerSeperation);

        // Act
        var result = await _service.UpdateAsync(username, userEmail, retailerSeperation, errorDetails);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(retailerSeperation);
    }

    [Fact]
    public async Task UpdateAsync_When_Unsuccessful_Throws_Exception()
    {
        // Arrange
        var username = _fixture.Create<string>();
        var userEmail = _fixture.Create<string>();
        var errorDetails = _fixture.Create<string>();
        var rsEditRequests = _fixture.Create<RetailerSeperationRequest>();

        _retailerSeperationRepositoryStub
            .Setup(b => b.GetJiraId(It.IsAny<int>()))
            .ThrowsAsync(new EntityNotExistsException($"No Retailer Separation Request exists with Request Id {rsEditRequests.Id}"));

        // Act
        var act = async () => await _service.UpdateAsync(username, userEmail, rsEditRequests, errorDetails);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>()
                  .WithMessage($"ENTITY_NOT_EXISTS");
    }

    [Fact]
    public async Task GetAsync_When_DetailsRetrieved_Returns_Success()
    {
        // Arrange
        var rsRequests = _fixture.Create<RetailerSeperationRequest>();
        var requestDetailId = 1;

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsync(requestDetailId))
            .ReturnsAsync(rsRequests);

        // Act
        var result = await _service.GetAsync(requestDetailId);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(rsRequests);
    }

    [Fact]
    public async Task GetAsync_When_NoDetailsRetrieved_Throws_Exception()
    {
        // Arrange
        var invalidId = 1010;

        _retailerSeperationRepositoryStub
            .Setup(b => b.GetAsync(invalidId))
            .ThrowsAsync(new EntityNotExistsException($"No Retailer Separation Request exists with Request Detail Id {invalidId}"));

        // Act
        var act = async () => await _service.GetAsync(invalidId);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>()
                  .WithMessage($"ENTITY_NOT_EXISTS");
    }

    [Fact]
    public async Task GetUsersList_When_NoUsersRetrieved_Expect_EmptyResult()
    {
        // Arrange
        var emptyUsersList = new List<string>().AsReadOnly();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetUsersList())
            .ReturnsAsync(emptyUsersList);

        // Act
        var result = await _service.GetUsersList();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task UpdateStatusAsync_ThrowsEntityNotExistsException_WhenRequestNotFound()
    {
        // Arrange
        var rsStatusDetails = new RetailerSeparationStatusDetails { RequestId = 1 };

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsync(rsStatusDetails.RequestId))
            .ThrowsAsync(new EntityNotExistsException($"No Retailer Separation Request exists with Request Detail Id {rsStatusDetails.RequestId}"));

        // Act
        var act = async () => await _service.GetAsync(rsStatusDetails.RequestId);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>()
                  .WithMessage($"ENTITY_NOT_EXISTS");
    }

    [Fact]
    public async Task UpdateStatusAsync_CallsPutJiraTicket_WhenDeclineStatusRequestFound()
    {
        // Arrange
        var rsStatusDetails = new RetailerSeparationStatusDetails
        {
            RequestId = 1,
            Username = "testUser",
            StatusId = 5,
            Reason = "Test Reason"
        };
        string comments = $"{rsStatusDetails.Username} has declined the Jira ticket due to this reason: {rsStatusDetails.Reason}";
        var rsRequest = new RetailerSeperationRequest { JiraId = "JIRA-123" };
        var jiraTicket = _fixture.Create<JiraTicket>();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsync(rsStatusDetails.RequestId))
            .ReturnsAsync(rsRequest);

        _jiraHelper.Setup(helper => helper.PutTicket(It.IsAny<TicketDetails>()))
            .ReturnsAsync(jiraTicket);

        _jiraHelper.Setup(helper => helper.GenerateTicketComments(It.IsAny<CommentDetails>()))
            .Returns(comments);

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.UpdateStatusAsync(It.IsAny<RetailerSeparationStatusDetails>()))
            .ReturnsAsync(true);

        // Act
        await _service.UpdateStatusAsync(rsStatusDetails);

        // Assert
        _jiraHelper.Verify(helper => helper.PutTicket(It.Is<TicketDetails>(td =>
            td.TicketId == rsRequest.JiraId &&
            td.TicketStatus == JiraConstants.Resolved &&
            td.Comments.Contains(rsStatusDetails.Username) &&
            td.Comments.Contains(rsStatusDetails.Reason) &&
            td.RestrictComment == false
        )), Times.Once);
    }

    [Fact]
    public async Task UpdateStatusAsync_CallsPutJiraTicket_WhenFinishedStatusRequestFound()
    {
        // Arrange
        var rsStatusDetails = new RetailerSeparationStatusDetails
        {
            RequestId = 1,
            Username = "testUser",
            StatusId = 3,
            Reason = ""
        };

        string comments = "Retailer Separation job is completed, and the ticket is Resolved.";
        var rsRequest = new RetailerSeperationRequest { JiraId = "JIRA-123" };
        var jiraTicket = _fixture.Create<JiraTicket>();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsync(rsStatusDetails.RequestId))
            .ReturnsAsync(rsRequest);

        _jiraHelper.Setup(helper => helper.PutTicket(It.IsAny<TicketDetails>()))
            .ReturnsAsync(jiraTicket);

        _jiraHelper.Setup(helper => helper.GenerateTicketComments(It.IsAny<CommentDetails>()))
            .Returns(comments);

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.UpdateStatusAsync(It.IsAny<RetailerSeparationStatusDetails>()))
            .ReturnsAsync(true);

        // Act
        await _service.UpdateStatusAsync(rsStatusDetails);

        // Assert
        _jiraHelper.Verify(helper => helper.PutTicket(It.Is<TicketDetails>(td =>
            td.TicketId == rsRequest.JiraId &&
            td.TicketStatus == JiraConstants.Resolved &&
            td.Comments.Contains("ticket is Resolved") &&
            td.RestrictComment == false
        )), Times.Once);
    }

    [Fact]
    public async Task UpdateStatusAsync_CallsUpdateStatusAsync_WhenRequestFound()
    {
        // Arrange
        var rsStatusDetails = new RetailerSeparationStatusDetails
        {
            RequestId = 1,
            Username = "testUser",
            StatusId = 1,
            Reason = "Test Reason"
        };
        var rsRequest = new RetailerSeperationRequest { JiraId = "JIRA-123" };
        var jiraTicket = _fixture.Create<JiraTicket>();

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.GetAsync(rsStatusDetails.RequestId))
            .ReturnsAsync(rsRequest);

        _jiraHelper
            .Setup(helper => helper.PutTicket(It.IsAny<TicketDetails>()))
            .ReturnsAsync(jiraTicket);

        _retailerSeperationRepositoryStub
           .Setup(repo => repo.UpdateStatusAsync(It.IsAny<RetailerSeparationStatusDetails>()))
           .ReturnsAsync(true);

        // Act
        await _service.UpdateStatusAsync(rsStatusDetails);

        // Assert
        _retailerSeperationRepositoryStub.Verify(repo => repo.UpdateStatusAsync(rsStatusDetails), Times.Once);
    }

    [Fact]
    public async Task UpdateStatusAsync_LogsError_WhenRequestNotFound()
    {
        // Arrange
        var rsStatusDetails = new RetailerSeparationStatusDetails
        {
            RequestId = 1,
            Username = "testUser",
            StatusId = 1,
            Reason = "Test Reason"
        };
        var rsRequest = new RetailerSeperationRequest { JiraId = "JIRA-123" };
        var jiraTicket = _fixture.Create<JiraTicket>();

        _retailerSeperationRepositoryStub
           .Setup(repo => repo.GetAsync(rsStatusDetails.RequestId))
           .ReturnsAsync(rsRequest);

        _jiraHelper.Setup(helper => helper.PutTicket(It.IsAny<TicketDetails>()))
            .ReturnsAsync(jiraTicket);

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.UpdateStatusAsync(It.IsAny<RetailerSeparationStatusDetails>()))
            .ReturnsAsync(false);


        // Act
        Func<Task> act = async () => await _service.UpdateStatusAsync(rsStatusDetails);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>();
        _loggerStub.Verify(logger => logger.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("UpdateStatusAsync EXCEPTION")),
            null,
            It.IsAny<Func<It.IsAnyType, Exception, string>>()), Times.Once);
    }
    [Fact]
    public async Task DeleteSourceBPAsync_WhenCalledWithValidIds_ShouldReturn_ListOfResponseInfoRetailerSeperation()
    {
        // Arrange
        var deleteRequest = new IRSeperationDeletes
        {
            RetailerSeperationIds = new List<int> { 101, 102, 103 }
        };

        var responseList = new List<ResponseInfoRetailerSeperation>
        {
            new ResponseInfoRetailerSeperation("101", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(),101),
            new ResponseInfoRetailerSeperation("102", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(),102),
            new ResponseInfoRetailerSeperation("103", (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString(),103)
        };

        _retailerSeperationRepositoryStub.Setup(repo => repo.DeleteSourceBPAsync(deleteRequest))
                                         .ReturnsAsync(responseList);

        // Act
        var result = await _service.DeleteSourceBPAsync(deleteRequest);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(responseList);
    }

    [Fact]
    public async Task DeleteSourceBPAsync_WhenDeletingNonExistentId_ShouldReturn_NotFoundInResponse()
    {
        // Arrange
        var deleteRequest = new IRSeperationDeletes
        {
            RetailerSeperationIds = new List<int> { 104 }
        };

        var responseList = new List<ResponseInfoRetailerSeperation>
        {
            new ResponseInfoRetailerSeperation("104", (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString(),101)
        };

        _retailerSeperationRepositoryStub.Setup(repo => repo.DeleteSourceBPAsync(deleteRequest))
                                         .ReturnsAsync(responseList);

        // Act
        var result = await _service.DeleteSourceBPAsync(deleteRequest);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        result.First().StatusCode.Should().Be((int)HttpStatusCode.NotFound);
        result.First().StatusMsg.Should().Be(HttpStatusCode.NotFound.ToString());
    }

    [Fact]
    public async Task DeleteSourceBPAsync_WhenCalledWithMultipleIds_ShouldReturnMixedStatus()
    {
        // Arrange
        var deleteRequest = new IRSeperationDeletes
        {
            RetailerSeperationIds = new List<int> { 106, 107, 108 }
        };

        var responseList = new List<ResponseInfoRetailerSeperation>
        {
            new ResponseInfoRetailerSeperation("106", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(),101),
            new ResponseInfoRetailerSeperation("107", (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString(),102),
            new ResponseInfoRetailerSeperation("108", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(),103)
        };

        _retailerSeperationRepositoryStub.Setup(repo => repo.DeleteSourceBPAsync(deleteRequest))
                                         .ReturnsAsync(responseList);

        // Act
        var result = await _service.DeleteSourceBPAsync(deleteRequest);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(3);
        result[0].StatusCode.Should().Be((int)HttpStatusCode.OK);
        result[1].StatusCode.Should().Be((int)HttpStatusCode.NotFound);
        result[2].StatusCode.Should().Be((int)HttpStatusCode.OK);
    }

    [Fact]
    public async Task AddAsync_When_QCStatusExists_Expect_UnSuccessfulResult()
    {
        // Arrange
        var email = _fixture.Create<string>();
        var origin = _fixture.Create<string>();
        var retailerSeperation = _fixture.Build<RetailerSeperationRequest>()
            .With(rs => rs.RetailerSeperations, _fixture.CreateMany<RetailerSeperation>(10).ToList())
            .Create();

        var qcStatuses = retailerSeperation.RetailerSeperations
            .Select(rs => rs.SourceBPId)
            .Take(2)
            .ToList();

        _baseProjectRepositoryStub
            .Setup(repo => repo.GetBaseProjectWithQCStatus(It.IsAny<QCStatuses>()))
            .ReturnsAsync(qcStatuses);

        // Act
        Func<Task> act = async () => await _service.AddAsync(retailerSeperation, email);

        // Assert
        await act.Should()
            .ThrowAsync<InvalidOperationException>()
            .WithMessage($"Selected BPs have at least one QC Period in QC Status. ({string.Join(", ", qcStatuses)})");
    }

    [Fact]
    public async Task PerformRetailerSeparationAsyncToRabbitMQ_When_Called_Expect_Correct_Methods_Called()
    {
        // Arrange
        var baseProjectId = 123;
        var typeId = 456;
        var username = "testUser";
        var retailerSeperationRequestId = 789;
        var indexSourceBP = 1;
        var totalSourceBP = 456;

        _mockoutBoxItemRepository
            .Setup(repo => repo.SaveMessagesAsync(
                It.Is<RetailerSeparationsData>(data =>
                    data.BaseProjectId == baseProjectId &&
                    data.TypeId == typeId &&
                    data.RetailerSeparationRequestId == retailerSeperationRequestId &&
                    data.username == username &&
                    Convert.ToInt32(data.SyncingEntityId) == baseProjectId),
                ProjectMessageType.RetailerSeparationRequest))
            .Returns(Task.CompletedTask);

        // Act
        await _service.PerformRetailerSeparationAsyncToRabbitMQ(baseProjectId, typeId, username, retailerSeperationRequestId, indexSourceBP, totalSourceBP);

        // Assert
        _mockoutBoxItemRepository.Verify(repo =>
            repo.SaveMessagesAsync(
                It.Is<RetailerSeparationsData>(data =>
                    data.BaseProjectId == baseProjectId &&
                    data.TypeId == typeId &&
                    data.RetailerSeparationRequestId == retailerSeperationRequestId &&
                    data.username == username &&
                    Convert.ToInt32(data.SyncingEntityId) == baseProjectId),
                ProjectMessageType.RetailerSeparationRequest),
            Times.Once);
    }



    [Fact]
    public async Task PerformRetailerSeparationAsync_When_CopyBPSecurityUsersIsCalled_Expect_CalledOnce()
    {
        // Arrange
        var baseProjectId = 123;
        var typeId = 456;
        var username = "testUser";
        var retailerSeperationRequestId = 789;
        var retailerBP = _fixture.Create<BaseProject>();
        var indexSourceBP = 1;
        var totalSourceBP = 456;

        _retailerSeperationRepositoryStub
            .Setup(repo => repo.PerformRetailerSeparation(baseProjectId, typeId, username, retailerSeperationRequestId))
            .ReturnsAsync(retailerBP);


        // Act
        var result = await _service.PerformRetailerSeparationAsync(baseProjectId, typeId, username, retailerSeperationRequestId, indexSourceBP, totalSourceBP);

        // Assert
        result.Should().ShouldNotBeNull();
        result.Should().Be(retailerBP.Id);
    }



}
